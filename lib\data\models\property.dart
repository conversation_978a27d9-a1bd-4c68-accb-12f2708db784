import 'package:json_annotation/json_annotation.dart';

part 'property.g.dart';

@JsonSerializable()
class Property {
  final String id;
  final String name;
  final String type; // residential, office, construction
  final String address;
  final String description;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<SystemStatus> systemStatuses;
  final PropertySettings? settings;

  const Property({
    required this.id,
    required this.name,
    required this.type,
    required this.address,
    required this.description,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    required this.systemStatuses,
    this.settings,
  });

  factory Property.fromJson(Map<String, dynamic> json) => _$PropertyFromJson(json);
  Map<String, dynamic> toJson() => _$PropertyToJson(this);

  Property copyWith({
    String? id,
    String? name,
    String? type,
    String? address,
    String? description,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<SystemStatus>? systemStatuses,
    PropertySettings? settings,
  }) {
    return Property(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      address: address ?? this.address,
      description: description ?? this.description,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      systemStatuses: systemStatuses ?? this.systemStatuses,
      settings: settings ?? this.settings,
    );
  }
}

@JsonSerializable()
class SystemStatus {
  final String id;
  final String propertyId;
  final String systemType; // water, electricity, security, internet, ott, maintenance
  final String status; // operational, warning, critical, offline
  final String? description;
  final DateTime lastUpdated;
  final Map<String, dynamic>? metadata;

  const SystemStatus({
    required this.id,
    required this.propertyId,
    required this.systemType,
    required this.status,
    this.description,
    required this.lastUpdated,
    this.metadata,
  });

  factory SystemStatus.fromJson(Map<String, dynamic> json) => _$SystemStatusFromJson(json);
  Map<String, dynamic> toJson() => _$SystemStatusToJson(this);

  SystemStatus copyWith({
    String? id,
    String? propertyId,
    String? systemType,
    String? status,
    String? description,
    DateTime? lastUpdated,
    Map<String, dynamic>? metadata,
  }) {
    return SystemStatus(
      id: id ?? this.id,
      propertyId: propertyId ?? this.propertyId,
      systemType: systemType ?? this.systemType,
      status: status ?? this.status,
      description: description ?? this.description,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      metadata: metadata ?? this.metadata,
    );
  }
}

@JsonSerializable()
class PropertySettings {
  final String propertyId;
  final Map<String, dynamic> waterSettings;
  final Map<String, dynamic> electricitySettings;
  final Map<String, dynamic> securitySettings;
  final Map<String, dynamic> internetSettings;
  final Map<String, dynamic> ottSettings;
  final Map<String, dynamic> maintenanceSettings;

  const PropertySettings({
    required this.propertyId,
    required this.waterSettings,
    required this.electricitySettings,
    required this.securitySettings,
    required this.internetSettings,
    required this.ottSettings,
    required this.maintenanceSettings,
  });

  factory PropertySettings.fromJson(Map<String, dynamic> json) => _$PropertySettingsFromJson(json);
  Map<String, dynamic> toJson() => _$PropertySettingsToJson(this);

  PropertySettings copyWith({
    String? propertyId,
    Map<String, dynamic>? waterSettings,
    Map<String, dynamic>? electricitySettings,
    Map<String, dynamic>? securitySettings,
    Map<String, dynamic>? internetSettings,
    Map<String, dynamic>? ottSettings,
    Map<String, dynamic>? maintenanceSettings,
  }) {
    return PropertySettings(
      propertyId: propertyId ?? this.propertyId,
      waterSettings: waterSettings ?? this.waterSettings,
      electricitySettings: electricitySettings ?? this.electricitySettings,
      securitySettings: securitySettings ?? this.securitySettings,
      internetSettings: internetSettings ?? this.internetSettings,
      ottSettings: ottSettings ?? this.ottSettings,
      maintenanceSettings: maintenanceSettings ?? this.maintenanceSettings,
    );
  }
}

@JsonSerializable()
class WaterSystem {
  final String id;
  final String propertyId;
  final List<String> municipalConnections;
  final BorewellInfo? borewellInfo;
  final WaterTankInfo tankInfo;
  final AutomationSystem? automationSystem;
  final List<ContactInfo> contacts;

  const WaterSystem({
    required this.id,
    required this.propertyId,
    required this.municipalConnections,
    this.borewellInfo,
    required this.tankInfo,
    this.automationSystem,
    required this.contacts,
  });

  factory WaterSystem.fromJson(Map<String, dynamic> json) => _$WaterSystemFromJson(json);
  Map<String, dynamic> toJson() => _$WaterSystemToJson(this);
}

@JsonSerializable()
class BorewellInfo {
  final String id;
  final double depth;
  final double waterLevel;
  final String quality;
  final DateTime lastTested;

  const BorewellInfo({
    required this.id,
    required this.depth,
    required this.waterLevel,
    required this.quality,
    required this.lastTested,
  });

  factory BorewellInfo.fromJson(Map<String, dynamic> json) => _$BorewellInfoFromJson(json);
  Map<String, dynamic> toJson() => _$BorewellInfoToJson(this);
}

@JsonSerializable()
class WaterTankInfo {
  final String id;
  final double capacity;
  final double currentLevel;
  final String material;
  final DateTime lastCleaned;

  const WaterTankInfo({
    required this.id,
    required this.capacity,
    required this.currentLevel,
    required this.material,
    required this.lastCleaned,
  });

  factory WaterTankInfo.fromJson(Map<String, dynamic> json) => _$WaterTankInfoFromJson(json);
  Map<String, dynamic> toJson() => _$WaterTankInfoToJson(this);
}

@JsonSerializable()
class AutomationSystem {
  final String id;
  final String brand;
  final String model;
  final bool isActive;
  final DateTime lastMaintenance;

  const AutomationSystem({
    required this.id,
    required this.brand,
    required this.model,
    required this.isActive,
    required this.lastMaintenance,
  });

  factory AutomationSystem.fromJson(Map<String, dynamic> json) => _$AutomationSystemFromJson(json);
  Map<String, dynamic> toJson() => _$AutomationSystemToJson(this);
}

@JsonSerializable()
class ContactInfo {
  final String id;
  final String name;
  final String phone;
  final String? email;
  final String role;
  final String category;

  const ContactInfo({
    required this.id,
    required this.name,
    required this.phone,
    this.email,
    required this.role,
    required this.category,
  });

  factory ContactInfo.fromJson(Map<String, dynamic> json) => _$ContactInfoFromJson(json);
  Map<String, dynamic> toJson() => _$ContactInfoToJson(this);
}
