import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_theme.dart';
import '../main/main_navigation_screen.dart';

class PropertyDetailScreen extends ConsumerStatefulWidget {
  final String propertyId;
  
  const PropertyDetailScreen({
    super.key,
    required this.propertyId,
  });

  @override
  ConsumerState<PropertyDetailScreen> createState() => _PropertyDetailScreenState();
}

class _PropertyDetailScreenState extends ConsumerState<PropertyDetailScreen> {
  @override
  Widget build(BuildContext context) {
    // Mock property data based on propertyId
    final property = _getPropertyData(widget.propertyId);
    
    return Scaffold(
      appBar: CustomAppBar(
        title: property['name'],
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              // TODO: Navigate to property settings
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Property Header
            _buildPropertyHeader(property),
            
            // System Navigation Grid
            _buildSystemNavigationGrid(property),
            
            // Quick Stats
            _buildQuickStats(property),
            
            // Recent Activities
            _buildRecentActivities(),
          ],
        ),
      ),
    );
  }

  Map<String, dynamic> _getPropertyData(String propertyId) {
    // Mock data - in real app, this would come from a provider/repository
    switch (propertyId) {
      case 'jublee-hills-home':
        return {
          'id': 'jublee-hills-home',
          'name': 'Jublee Hills Home',
          'type': 'residential',
          'address': 'Road No. 36, Jubilee Hills, Hyderabad',
          'status': 'operational',
          'description': 'Primary residential property with comprehensive management systems',
          'systems': [
            {'type': 'water', 'status': 'operational', 'lastUpdated': '2 hours ago'},
            {'type': 'electricity', 'status': 'operational', 'lastUpdated': '1 hour ago'},
            {'type': 'security', 'status': 'operational', 'lastUpdated': '30 minutes ago'},
            {'type': 'internet', 'status': 'operational', 'lastUpdated': '1 hour ago'},
            {'type': 'ott', 'status': 'operational', 'lastUpdated': '3 hours ago'},
            {'type': 'maintenance', 'status': 'warning', 'lastUpdated': '4 hours ago'},
          ],
        };
      case 'gandipet-guest-house':
        return {
          'id': 'gandipet-guest-house',
          'name': 'Gandipet Guest House',
          'type': 'residential',
          'address': 'Gandipet, Hyderabad',
          'status': 'warning',
          'description': 'Guest house property with essential management systems',
          'systems': [
            {'type': 'water', 'status': 'operational', 'lastUpdated': '1 hour ago'},
            {'type': 'electricity', 'status': 'warning', 'lastUpdated': '2 hours ago'},
            {'type': 'security', 'status': 'operational', 'lastUpdated': '1 hour ago'},
            {'type': 'internet', 'status': 'operational', 'lastUpdated': '30 minutes ago'},
            {'type': 'maintenance', 'status': 'operational', 'lastUpdated': '2 hours ago'},
          ],
        };
      default:
        return {
          'id': propertyId,
          'name': 'Property',
          'type': 'unknown',
          'address': 'Unknown Address',
          'status': 'offline',
          'description': 'Property information not available',
          'systems': [],
        };
    }
  }

  Widget _buildPropertyHeader(Map<String, dynamic> property) {
    Color statusColor;
    switch (property['status']) {
      case 'operational':
        statusColor = AppTheme.successColor;
        break;
      case 'warning':
        statusColor = AppTheme.warningColor;
        break;
      case 'critical':
        statusColor = AppTheme.errorColor;
        break;
      default:
        statusColor = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.8),
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      property['name'],
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      property['address'],
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: statusColor,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  property['status'].toString().toUpperCase(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          Text(
            property['description'],
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.white.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSystemNavigationGrid(Map<String, dynamic> property) {
    final systemsRaw = property['systems'] as List<dynamic>;
    final systems = systemsRaw.cast<Map<String, dynamic>>();
    
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'System Management',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 16),
          
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.2,
            ),
            itemCount: systems.length,
            itemBuilder: (context, index) {
              final system = systems[index];
              return _buildSystemCard(system);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSystemCard(Map<String, dynamic> system) {
    Color statusColor;
    IconData systemIcon;
    String systemName;
    
    switch (system['type']) {
      case 'water':
        systemIcon = Icons.water_drop;
        systemName = 'Water Management';
        break;
      case 'electricity':
        systemIcon = Icons.electrical_services;
        systemName = 'Electricity';
        break;
      case 'security':
        systemIcon = Icons.security;
        systemName = 'Security';
        break;
      case 'internet':
        systemIcon = Icons.wifi;
        systemName = 'Internet';
        break;
      case 'ott':
        systemIcon = Icons.tv;
        systemName = 'OTT Services';
        break;
      case 'maintenance':
        systemIcon = Icons.build;
        systemName = 'Maintenance';
        break;
      default:
        systemIcon = Icons.settings;
        systemName = 'System';
    }

    switch (system['status']) {
      case 'operational':
        statusColor = AppTheme.successColor;
        break;
      case 'warning':
        statusColor = AppTheme.warningColor;
        break;
      case 'critical':
        statusColor = AppTheme.errorColor;
        break;
      default:
        statusColor = Colors.grey;
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          // Navigate to specific system management screen
          context.go('/properties/${widget.propertyId}/${system['type']}');
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // System Icon with Status
              Stack(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: statusColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      systemIcon,
                      color: statusColor,
                      size: 24,
                    ),
                  ),
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: statusColor,
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 2),
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // System Name
              Text(
                systemName,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 4),
              
              // Last Updated
              Text(
                'Updated ${system['lastUpdated']}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickStats(Map<String, dynamic> property) {
    final systemsRaw = property['systems'] as List<dynamic>;
    final systems = systemsRaw.cast<Map<String, dynamic>>();
    final operationalCount = systems.where((s) => s['status'] == 'operational').length;
    final warningCount = systems.where((s) => s['status'] == 'warning').length;
    final criticalCount = systems.where((s) => s['status'] == 'critical').length;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Statistics',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Total Systems',
                  systems.length.toString(),
                  Icons.dashboard,
                  AppTheme.infoColor,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Operational',
                  operationalCount.toString(),
                  Icons.check_circle,
                  AppTheme.successColor,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Warnings',
                  warningCount.toString(),
                  Icons.warning,
                  AppTheme.warningColor,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Critical',
                  criticalCount.toString(),
                  Icons.error,
                  AppTheme.errorColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivities() {
    final activities = [
      {
        'title': 'Generator fuel updated',
        'description': 'Fuel level updated to 60%',
        'time': '2 hours ago',
        'type': 'electricity',
      },
      {
        'title': 'Security guard check-in',
        'description': 'Bhudev Kumar started day shift',
        'time': '3 hours ago',
        'type': 'security',
      },
      {
        'title': 'Water tank automation check',
        'description': 'Ktronics system running normally',
        'time': '4 hours ago',
        'type': 'water',
      },
    ];

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Activities',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              TextButton(
                onPressed: () {
                  // TODO: Navigate to full activity log
                },
                child: const Text('View All'),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          ...activities.map((activity) => _buildActivityCard(activity)),
        ],
      ),
    );
  }

  Widget _buildActivityCard(Map<String, dynamic> activity) {
    IconData activityIcon;
    Color activityColor;
    
    switch (activity['type']) {
      case 'water':
        activityIcon = Icons.water_drop;
        activityColor = Colors.blue;
        break;
      case 'electricity':
        activityIcon = Icons.electrical_services;
        activityColor = Colors.orange;
        break;
      case 'security':
        activityIcon = Icons.security;
        activityColor = Colors.green;
        break;
      default:
        activityIcon = Icons.info;
        activityColor = Colors.grey;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: activityColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            activityIcon,
            color: activityColor,
            size: 20,
          ),
        ),
        title: Text(
          activity['title'],
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(activity['description']),
            const SizedBox(height: 2),
            Text(
              activity['time'],
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        trailing: Icon(
          Icons.chevron_right,
          color: Colors.grey[400],
        ),
        onTap: () {
          // TODO: Navigate to activity detail
        },
      ),
    );
  }
}
