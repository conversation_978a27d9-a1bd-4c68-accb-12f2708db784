import 'package:json_annotation/json_annotation.dart';

part 'office.g.dart';

@JsonSerializable()
class Office {
  final String id;
  final String name;
  final String address;
  final String type; // back_office, strf_office, road_office
  final bool isActive;
  final List<Employee> employees;
  final OfficeSettings settings;

  const Office({
    required this.id,
    required this.name,
    required this.address,
    required this.type,
    required this.isActive,
    required this.employees,
    required this.settings,
  });

  factory Office.fromJson(Map<String, dynamic> json) => _$OfficeFromJson(json);
  Map<String, dynamic> toJson() => _$OfficeToJson(this);

  Office copyWith({
    String? id,
    String? name,
    String? address,
    String? type,
    bool? isActive,
    List<Employee>? employees,
    OfficeSettings? settings,
  }) {
    return Office(
      id: id ?? this.id,
      name: name ?? this.name,
      address: address ?? this.address,
      type: type ?? this.type,
      isActive: isActive ?? this.isActive,
      employees: employees ?? this.employees,
      settings: settings ?? this.settings,
    );
  }
}

@JsonSerializable()
class Employee {
  final String id;
  final String name;
  final String mobile;
  final String role; // full_time, contract, support_staff
  final String dutyStartTime;
  final String dutyEndTime;
  final String? team;
  final String? remarks;
  final bool isActive;
  final DateTime joinedDate;

  const Employee({
    required this.id,
    required this.name,
    required this.mobile,
    required this.role,
    required this.dutyStartTime,
    required this.dutyEndTime,
    this.team,
    this.remarks,
    required this.isActive,
    required this.joinedDate,
  });

  factory Employee.fromJson(Map<String, dynamic> json) => _$EmployeeFromJson(json);
  Map<String, dynamic> toJson() => _$EmployeeToJson(this);

  Employee copyWith({
    String? id,
    String? name,
    String? mobile,
    String? role,
    String? dutyStartTime,
    String? dutyEndTime,
    String? team,
    String? remarks,
    bool? isActive,
    DateTime? joinedDate,
  }) {
    return Employee(
      id: id ?? this.id,
      name: name ?? this.name,
      mobile: mobile ?? this.mobile,
      role: role ?? this.role,
      dutyStartTime: dutyStartTime ?? this.dutyStartTime,
      dutyEndTime: dutyEndTime ?? this.dutyEndTime,
      team: team ?? this.team,
      remarks: remarks ?? this.remarks,
      isActive: isActive ?? this.isActive,
      joinedDate: joinedDate ?? this.joinedDate,
    );
  }

  int get workingHours {
    final start = _parseTime(dutyStartTime);
    final end = _parseTime(dutyEndTime);
    return end.difference(start).inHours;
  }

  DateTime _parseTime(String timeString) {
    final parts = timeString.split(':');
    final hour = int.parse(parts[0]);
    final minute = int.parse(parts[1]);
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day, hour, minute);
  }
}

@JsonSerializable()
class Attendance {
  final String id;
  final String employeeId;
  final String officeId;
  final DateTime date;
  final String status; // present, absent, late, half_day
  final String? checkInTime;
  final String? checkOutTime;
  final int hoursWorked;
  final String? remarks;
  final DateTime createdAt;

  const Attendance({
    required this.id,
    required this.employeeId,
    required this.officeId,
    required this.date,
    required this.status,
    this.checkInTime,
    this.checkOutTime,
    required this.hoursWorked,
    this.remarks,
    required this.createdAt,
  });

  factory Attendance.fromJson(Map<String, dynamic> json) => _$AttendanceFromJson(json);
  Map<String, dynamic> toJson() => _$AttendanceToJson(this);

  Attendance copyWith({
    String? id,
    String? employeeId,
    String? officeId,
    DateTime? date,
    String? status,
    String? checkInTime,
    String? checkOutTime,
    int? hoursWorked,
    String? remarks,
    DateTime? createdAt,
  }) {
    return Attendance(
      id: id ?? this.id,
      employeeId: employeeId ?? this.employeeId,
      officeId: officeId ?? this.officeId,
      date: date ?? this.date,
      status: status ?? this.status,
      checkInTime: checkInTime ?? this.checkInTime,
      checkOutTime: checkOutTime ?? this.checkOutTime,
      hoursWorked: hoursWorked ?? this.hoursWorked,
      remarks: remarks ?? this.remarks,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

@JsonSerializable()
class ConstructionSite {
  final String id;
  final String name;
  final String location;
  final String status; // active, completed, on_hold
  final List<Worker> workers;
  final DateTime startDate;
  final DateTime? expectedEndDate;

  const ConstructionSite({
    required this.id,
    required this.name,
    required this.location,
    required this.status,
    required this.workers,
    required this.startDate,
    this.expectedEndDate,
  });

  factory ConstructionSite.fromJson(Map<String, dynamic> json) => _$ConstructionSiteFromJson(json);
  Map<String, dynamic> toJson() => _$ConstructionSiteToJson(this);

  ConstructionSite copyWith({
    String? id,
    String? name,
    String? location,
    String? status,
    List<Worker>? workers,
    DateTime? startDate,
    DateTime? expectedEndDate,
  }) {
    return ConstructionSite(
      id: id ?? this.id,
      name: name ?? this.name,
      location: location ?? this.location,
      status: status ?? this.status,
      workers: workers ?? this.workers,
      startDate: startDate ?? this.startDate,
      expectedEndDate: expectedEndDate ?? this.expectedEndDate,
    );
  }
}

@JsonSerializable()
class Worker {
  final String id;
  final String name;
  final String role; // helper, gardener, security_guard, supervisor
  final String dutyStartTime;
  final String dutyEndTime;
  final String? mobile;
  final String shift; // day, night
  final bool isActive;

  const Worker({
    required this.id,
    required this.name,
    required this.role,
    required this.dutyStartTime,
    required this.dutyEndTime,
    this.mobile,
    required this.shift,
    required this.isActive,
  });

  factory Worker.fromJson(Map<String, dynamic> json) => _$WorkerFromJson(json);
  Map<String, dynamic> toJson() => _$WorkerToJson(this);

  Worker copyWith({
    String? id,
    String? name,
    String? role,
    String? dutyStartTime,
    String? dutyEndTime,
    String? mobile,
    String? shift,
    bool? isActive,
  }) {
    return Worker(
      id: id ?? this.id,
      name: name ?? this.name,
      role: role ?? this.role,
      dutyStartTime: dutyStartTime ?? this.dutyStartTime,
      dutyEndTime: dutyEndTime ?? this.dutyEndTime,
      mobile: mobile ?? this.mobile,
      shift: shift ?? this.shift,
      isActive: isActive ?? this.isActive,
    );
  }
}

@JsonSerializable()
class WorkerAttendance {
  final String id;
  final String workerId;
  final String siteId;
  final DateTime date;
  final String status; // present, absent
  final int hoursWorked;
  final String? remarks;

  const WorkerAttendance({
    required this.id,
    required this.workerId,
    required this.siteId,
    required this.date,
    required this.status,
    required this.hoursWorked,
    this.remarks,
  });

  factory WorkerAttendance.fromJson(Map<String, dynamic> json) => _$WorkerAttendanceFromJson(json);
  Map<String, dynamic> toJson() => _$WorkerAttendanceToJson(this);
}

@JsonSerializable()
class OfficeSettings {
  final String officeId;
  final String workingDaysPattern; // monday_to_friday, monday_to_saturday, etc.
  final String defaultStartTime;
  final String defaultEndTime;
  final bool allowFlexibleTiming;
  final int gracePeriodMinutes;
  final bool requireCheckOut;

  const OfficeSettings({
    required this.officeId,
    required this.workingDaysPattern,
    required this.defaultStartTime,
    required this.defaultEndTime,
    required this.allowFlexibleTiming,
    required this.gracePeriodMinutes,
    required this.requireCheckOut,
  });

  factory OfficeSettings.fromJson(Map<String, dynamic> json) => _$OfficeSettingsFromJson(json);
  Map<String, dynamic> toJson() => _$OfficeSettingsToJson(this);
}

@JsonSerializable()
class AttendanceReport {
  final String officeId;
  final DateTime startDate;
  final DateTime endDate;
  final String reportType; // daily, weekly, monthly
  final List<AttendanceSummary> summaries;
  final int totalWorkingDays;
  final int totalEmployees;

  const AttendanceReport({
    required this.officeId,
    required this.startDate,
    required this.endDate,
    required this.reportType,
    required this.summaries,
    required this.totalWorkingDays,
    required this.totalEmployees,
  });

  factory AttendanceReport.fromJson(Map<String, dynamic> json) => _$AttendanceReportFromJson(json);
  Map<String, dynamic> toJson() => _$AttendanceReportToJson(this);
}

@JsonSerializable()
class AttendanceSummary {
  final String employeeId;
  final String employeeName;
  final int presentDays;
  final int absentDays;
  final int lateDays;
  final int halfDays;
  final double attendancePercentage;
  final int totalHoursWorked;

  const AttendanceSummary({
    required this.employeeId,
    required this.employeeName,
    required this.presentDays,
    required this.absentDays,
    required this.lateDays,
    required this.halfDays,
    required this.attendancePercentage,
    required this.totalHoursWorked,
  });

  factory AttendanceSummary.fromJson(Map<String, dynamic> json) => _$AttendanceSummaryFromJson(json);
  Map<String, dynamic> toJson() => _$AttendanceSummaryToJson(this);
}
