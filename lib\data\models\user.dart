import 'package:json_annotation/json_annotation.dart';

part 'user.g.dart';

@JsonSerializable()
class User {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String role;
  final List<String> assignedProperties;
  final bool isActive;
  final DateTime createdAt;
  final DateTime lastLogin;
  final UserPermissions permissions;

  const User({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.role,
    required this.assignedProperties,
    required this.isActive,
    required this.createdAt,
    required this.lastLogin,
    required this.permissions,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);

  User copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? role,
    List<String>? assignedProperties,
    bool? isActive,
    DateTime? createdAt,
    DateTime? lastLogin,
    UserPermissions? permissions,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      role: role ?? this.role,
      assignedProperties: assignedProperties ?? this.assignedProperties,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
      permissions: permissions ?? this.permissions,
    );
  }
}

@JsonSerializable()
class UserPermissions {
  final bool canViewDashboard;
  final bool canManageProperties;
  final bool canManageOffice;
  final bool canManageSecurity;
  final bool canManageMaintenance;
  final bool canManageUsers;
  final bool canViewReports;
  final bool canExportData;
  final List<String> allowedScreens;
  final List<String> allowedActions;

  const UserPermissions({
    required this.canViewDashboard,
    required this.canManageProperties,
    required this.canManageOffice,
    required this.canManageSecurity,
    required this.canManageMaintenance,
    required this.canManageUsers,
    required this.canViewReports,
    required this.canExportData,
    required this.allowedScreens,
    required this.allowedActions,
  });

  factory UserPermissions.fromJson(Map<String, dynamic> json) => _$UserPermissionsFromJson(json);
  Map<String, dynamic> toJson() => _$UserPermissionsToJson(this);

  // Role-based permission factory methods
  factory UserPermissions.superAdmin() {
    return const UserPermissions(
      canViewDashboard: true,
      canManageProperties: true,
      canManageOffice: true,
      canManageSecurity: true,
      canManageMaintenance: true,
      canManageUsers: true,
      canViewReports: true,
      canExportData: true,
      allowedScreens: [
        'dashboard',
        'properties',
        'office_management',
        'settings',
        'user_management',
        'reports',
      ],
      allowedActions: [
        'create',
        'read',
        'update',
        'delete',
        'export',
        'import',
      ],
    );
  }

  factory UserPermissions.propertyManager() {
    return const UserPermissions(
      canViewDashboard: true,
      canManageProperties: true,
      canManageOffice: false,
      canManageSecurity: true,
      canManageMaintenance: true,
      canManageUsers: false,
      canViewReports: true,
      canExportData: true,
      allowedScreens: [
        'dashboard',
        'properties',
        'maintenance',
        'security',
        'reports',
      ],
      allowedActions: [
        'create',
        'read',
        'update',
        'export',
      ],
    );
  }

  factory UserPermissions.officeManager() {
    return const UserPermissions(
      canViewDashboard: true,
      canManageProperties: false,
      canManageOffice: true,
      canManageSecurity: false,
      canManageMaintenance: false,
      canManageUsers: false,
      canViewReports: true,
      canExportData: true,
      allowedScreens: [
        'dashboard',
        'office_management',
        'attendance',
        'reports',
      ],
      allowedActions: [
        'create',
        'read',
        'update',
        'export',
      ],
    );
  }

  factory UserPermissions.securityPersonnel() {
    return const UserPermissions(
      canViewDashboard: true,
      canManageProperties: false,
      canManageOffice: false,
      canManageSecurity: true,
      canManageMaintenance: false,
      canManageUsers: false,
      canViewReports: false,
      canExportData: false,
      allowedScreens: [
        'dashboard',
        'security',
        'cctv',
        'guard_logs',
      ],
      allowedActions: [
        'create',
        'read',
        'update',
      ],
    );
  }

  factory UserPermissions.maintenanceStaff() {
    return const UserPermissions(
      canViewDashboard: true,
      canManageProperties: false,
      canManageOffice: false,
      canManageSecurity: false,
      canManageMaintenance: true,
      canManageUsers: false,
      canViewReports: false,
      canExportData: false,
      allowedScreens: [
        'dashboard',
        'maintenance',
        'issues',
      ],
      allowedActions: [
        'create',
        'read',
        'update',
      ],
    );
  }

  factory UserPermissions.constructionSupervisor() {
    return const UserPermissions(
      canViewDashboard: true,
      canManageProperties: false,
      canManageOffice: true,
      canManageSecurity: false,
      canManageMaintenance: false,
      canManageUsers: false,
      canViewReports: true,
      canExportData: true,
      allowedScreens: [
        'dashboard',
        'construction_sites',
        'attendance',
        'reports',
      ],
      allowedActions: [
        'create',
        'read',
        'update',
        'export',
      ],
    );
  }
}

@JsonSerializable()
class AuthToken {
  final String accessToken;
  final String refreshToken;
  final DateTime expiresAt;
  final String tokenType;

  const AuthToken({
    required this.accessToken,
    required this.refreshToken,
    required this.expiresAt,
    required this.tokenType,
  });

  factory AuthToken.fromJson(Map<String, dynamic> json) => _$AuthTokenFromJson(json);
  Map<String, dynamic> toJson() => _$AuthTokenToJson(this);

  bool get isExpired => DateTime.now().isAfter(expiresAt);
  bool get isValid => !isExpired && accessToken.isNotEmpty;
}

@JsonSerializable()
class LoginRequest {
  final String email;
  final String password;
  final String? deviceId;
  final String? deviceName;

  const LoginRequest({
    required this.email,
    required this.password,
    this.deviceId,
    this.deviceName,
  });

  factory LoginRequest.fromJson(Map<String, dynamic> json) => _$LoginRequestFromJson(json);
  Map<String, dynamic> toJson() => _$LoginRequestToJson(this);
}

@JsonSerializable()
class LoginResponse {
  final User user;
  final AuthToken token;
  final String message;

  const LoginResponse({
    required this.user,
    required this.token,
    required this.message,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) => _$LoginResponseFromJson(json);
  Map<String, dynamic> toJson() => _$LoginResponseToJson(this);
}
